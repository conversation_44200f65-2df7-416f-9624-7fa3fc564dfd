services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost:8080/api/
      - API_URL=http://backend:8080/api/
    depends_on:
      - backend
    networks:
      - app-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - DATABASE_URL=mysql://root:123456@db-mysql:3306/DBDKKHAMBENH
      - DB_HOST=db-mysql
      - DB_PORT=3306
      - DB_NAME=DBDKKHAMBENH
      - DB_USER=root
      - DB_PASSWORD=123456
      - ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000
    depends_on:
      - db-mysql
    networks:
      - app-network

  db-mysql:
    image: mysql:8.0
    restart: always
    environment:
      - MYSQL_DATABASE=DBDKKHAMBENH
      - MYSQL_ROOT_PASSWORD=123456
    ports:
      - "3307:3306"
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - app-network

volumes:
  db_data:

networks:
  app-network:
    driver: bridge